<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from robot.xacro                    | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="robot">
  <material name="black">
    <color rgba="0.0 0.0 0.0 1.0"/>
  </material>
  <material name="blue">
    <color rgba="0.0 0.0 0.8 1.0"/>
  </material>
  <material name="green">
    <color rgba="0.0 0.8 0.0 1.0"/>
  </material>
  <material name="grey">
    <color rgba="0.2 0.2 0.2 1.0"/>
  </material>
  <material name="silver">
    <color rgba="0.9137254901960784 0.9137254901960784 0.8470588235294118 1.0"/>
  </material>
  <material name="orange">
    <color rgba="1.0 0.4235294117647059 0.0392156862745098 1.0"/>
  </material>
  <material name="brown">
    <color rgba="0.8705882352941177 0.8117647058823529 0.7647058823529411 1.0"/>
  </material>
  <material name="red">
    <color rgba="0.8 0.0 0.0 1.0"/>
  </material>
  <material name="white">
    <color rgba="1.0 1.0 1.0 1.0"/>
  </material>
  <!-- ros_control plugin -->
  <gazebo>
    <plugin filename="liblegged_hw_sim.so" name="gazebo_ros_control">
      <robotNamespace>/</robotNamespace>
      <robotParam>legged_robot_description</robotParam>
      <robotSimType>legged_gazebo/LeggedHWSim</robotSimType>
    </plugin>
  </gazebo>
  <gazebo>
    <plugin filename="libgazebo_ros_p3d.so" name="p3d_base_controller">
      <alwaysOn>true</alwaysOn>
      <updateRate>1000.0</updateRate>
      <bodyName>base</bodyName>
      <topicName>ground_truth/state</topicName>
      <gaussianNoise>0</gaussianNoise>
      <frameName>world</frameName>
      <xyzOffsets>0 0 0</xyzOffsets>
      <rpyOffsets>0 0 0</rpyOffsets>
    </plugin>
  </gazebo>
  <gazebo reference="base_link">
    <maxVel>100.</maxVel>
    <kp>10000.</kp>
    <kd>0.</kd>
  </gazebo>
  <gazebo reference="stick_link">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <material>Gazebo/White</material>
  </gazebo>
  <!-- LF leg -->
  <gazebo reference="LF_hip">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <material>Gazebo/DarkGrey</material>
  </gazebo>
  <gazebo reference="LF_thigh">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <self_collide>1</self_collide>
    <material>Gazebo/DarkGrey</material>
    <kp value="1000000.0"/>
    <kd value="1.0"/>
  </gazebo>
  <gazebo reference="LF_calf">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <self_collide>1</self_collide>
  </gazebo>
  <gazebo reference="LF_FOOT">
    <mu1>100</mu1>
    <mu2>100</mu2>
    <self_collide>1</self_collide>
    <material>Gazebo/DarkGrey</material>
    <kp value="1000000.0"/>
    <kd value="100.0"/>
  </gazebo>
  <!-- RF leg -->
  <gazebo reference="RF_hip">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <material>Gazebo/DarkGrey</material>
  </gazebo>
  <gazebo reference="RF_thigh">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <self_collide>1</self_collide>
    <material>Gazebo/DarkGrey</material>
    <kp value="1000000.0"/>
    <kd value="1.0"/>
  </gazebo>
  <gazebo reference="RF_calf">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <self_collide>1</self_collide>
  </gazebo>
  <gazebo reference="RF_FOOT">
    <mu1>100</mu1>
    <mu2>100</mu2>
    <self_collide>1</self_collide>
    <material>Gazebo/DarkGrey</material>
    <kp value="1000000.0"/>
    <kd value="1.0"/>
  </gazebo>
  <!-- LH leg -->
  <gazebo reference="LH_hip">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <material>Gazebo/DarkGrey</material>
  </gazebo>
  <gazebo reference="LH_thigh">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <self_collide>1</self_collide>
    <material>Gazebo/DarkGrey</material>
    <kp value="1000000.0"/>
    <kd value="1.0"/>
  </gazebo>
  <gazebo reference="LH_calf">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <self_collide>1</self_collide>
  </gazebo>
  <gazebo reference="LH_FOOT">
    <mu1>100</mu1>
    <mu2>100</mu2>
    <self_collide>1</self_collide>
    <material>Gazebo/DarkGrey</material>
    <kp value="1000000.0"/>
    <kd value="1.0"/>
  </gazebo>
  <!-- RH leg -->
  <gazebo reference="RH_hip">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <material>Gazebo/DarkGrey</material>
  </gazebo>
  <gazebo reference="RH_thigh">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <self_collide>1</self_collide>
    <material>Gazebo/DarkGrey</material>
    <kp value="1000000.0"/>
    <kd value="1.0"/>
  </gazebo>
  <gazebo reference="RH_calf">
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
    <self_collide>1</self_collide>
  </gazebo>
  <gazebo reference="RH_FOOT">
    <mu1>100</mu1>
    <mu2>100</mu2>
    <self_collide>1</self_collide>
    <material>Gazebo/DarkGrey</material>
    <kp value="1000000.0"/>
    <kd value="1.0"/>
  </gazebo>
  <!-- <xacro:property name="trunk_offset_z" value="0.0"/>
    <xacro:property name="hip_offset" value="0.065"/> -->
  <!-- 大小腿收拢限位图 -->
  <!-- <xacro:property name="calf_max" value="${-0.65}"/> -->
  <!-- <xacro:property name="calf_max" value="${-0.9}"/>
    <xacro:property name="calf_min" value="${-2.6195}"/> -->
  <!-- <xacro:property name="foot_com_x" value="0.00003045"/>
    <xacro:property name="foot_com_y" value="0"/>
    <xacro:property name="foot_com_z" value="-0.00104172"/> -->
  <!-- <xacro:property name="foot_ixx" value="0.00001086"/>
    <xacro:property name="foot_ixy" value="0"/>
    <xacro:property name="foot_ixz" value="0"/>
    <xacro:property name="foot_iyy" value="0.00001311"/>
    <xacro:property name="foot_iyz" value="0"/>
    <xacro:property name="foot_izz" value="0.00001277"/> -->
  <!-- <xacro:property name="foot_ixx" value="0.00001090"/>
    <xacro:property name="foot_ixy" value="0"/>
    <xacro:property name="foot_ixz" value="0"/>
    <xacro:property name="foot_iyy" value="0.00001316"/>
    <xacro:property name="foot_iyz" value="0"/>
    <xacro:property name="foot_izz" value="0.00001277"/> -->
  <!-- <link name="world" />

    <joint name ="weld" type="fixed">
        <parent link="world"/>
        <child link="base"/>
        <origin xyz="0 0 1"/>
    </joint> -->
  <link name="base">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://legged_robs3go_description/meshes/robs3go/trunk.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.512 0.216 0.152"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.02398199 -0.00100582 -0.00545947"/>
      <!-- <mesh filename="package://legged_robs3go_description/meshes/$(arg robot_type)/trunk.STL" scale="1 1 1"/> -->
      <mass value="7.86644305"/>
      <inertia ixx="0.03463619" ixy="3.246e-05" ixz="-0.00139993" iyy="0.10385097" iyz="-8.132e-05" izz="0.12206175"/>
    </inertial>
  </link>
  <!-- Imu is fixed to the base link -->
  <joint name="base_imu_joint" type="fixed">
    <origin rpy="0. 0. 0." xyz="0. 0. 0."/>
    <parent link="base"/>
    <child link="base_imu"/>
  </joint>
  <!-- Imu link -->
  <link name="base_imu">
    <inertial>
      <mass value="0.01"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.000001" ixy="0" ixz="0" iyy="0.000001" iyz="0" izz="0.000001"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.015 0.015 0.004"/>
      </geometry>
    </visual>
    <material name="orange">
      <color rgba="255 108 10 255"/>
    </material>
  </link>
  <gazebo reference="base_imu">
    <material>Gazebo/Orange</material>
  </gazebo>
  <gazebo reference="base_imu_joint">
    <disableFixedJointLumping>true</disableFixedJointLumping>
  </gazebo>
  <joint name="RF_HAA" type="revolute">
    <origin rpy="0 0 0" xyz="0.21 -0.055 0.0"/>
    <parent link="base"/>
    <child link="RF_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-1.0471975511965976" upper="1.0471975511965976" velocity="20.0"/>
  </joint>
  <link name="RF_hip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <!-- <origin rpy="${pi} 0 0" xyz="0 0 0"/> -->
      <geometry>
        <mesh filename="package://legged_robs3go_description/meshes/robs3go/FR_hip_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.028" radius="0.056"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00234792 -0.00037524 -9.86e-05"/>
      <mass value="0.95120267"/>
      <inertia ixx="0.0007136" ixy="-8.15e-06" ixz="-3.85e-06" iyy="0.00116168" iyz="3.3e-07" izz="0.00079101"/>
    </inertial>
  </link>
  <joint name="RF_HFE" type="revolute">
    <!-- c -->
    <!-- <origin rpy="0 0 0" xyz="0 ${thigh_offset*mirror} 0"/> -->
    <origin rpy="0 0.0 0" xyz="0 -0.09772 0"/>
    <parent link="RF_hip"/>
    <child link="RF_thigh"/>
    <!-- <axis xyz="0 -1 0"/> -->
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-1" upper="3.18871654339364" velocity="20.0"/>
  </joint>
  <link name="RF_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://legged_robs3go_description/meshes/robs3go/FR_thigh_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.12"/>
      <geometry>
        <box size="0.24 0.023 0.046"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.053" radius="0.056"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00370878 0.02484263 -0.02475346"/>
      <mass value="1.35348511"/>
      <inertia ixx="0.00599998" ixy="-0.00013853" ixz="-0.00051908" iyy="0.00615951" iyz="-0.00083398" izz="0.00132322"/>
    </inertial>
  </link>
  <joint name="RF_KFE" type="revolute">
    <!-- cc -->
    <origin rpy="0 0 0" xyz="0 0 -0.24"/>
    <parent link="RF_thigh"/>
    <child link="RF_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-2.743657584135086" upper="-0.6510078109938848" velocity="20.0"/>
  </joint>
  <link name="RF_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://legged_robs3go_description/meshes/robs3go/FR_calf_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.12"/>
      <geometry>
        <box size="0.24 0.023 0.045"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.00901673 -6.91e-06 -0.14084939"/>
      <mass value="0.21273957"/>
      <inertia ixx="0.00177407" ixy="-2e-08" ixz="8.082e-05" iyy="0.0017994" iyz="2.4e-07" izz="5.853e-05"/>
    </inertial>
  </link>
  <joint name="RF_foot_fixed" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 -0.24"/>
    <parent link="RF_calf"/>
    <child link="RF_FOOT"/>
  </joint>
  <link name="RF_FOOT">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
        <!-- <mesh filename="package://legged_robs3go_description/meshes/$(arg robot_type)/FL_toe_link.STL" scale="1 1 1"/> -->
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.0450952"/>
      <inertia ixx="1.1273800000000004e-05" ixy="0.0" ixz="0.0" iyy="1.1273800000000004e-05" iyz="0.0" izz="1.1273800000000004e-05"/>
    </inertial>
    <!-- <inertial>
                <mass value="${0.0}"/>
                <inertia
                        ixx="0.0" ixy="0.0" ixz="0.0"
                        iyy="0.0" iyz="0.0"
                        izz="0.0"/>
            </inertial> -->
    <!-- <inertial>
                <mass value="${foot_mass}"/>
                <origin rpy="0 0 0" xyz="${foot_com_x} ${foot_com_y} ${foot_com_z}"/>
                <inertia
                        ixx="${foot_ixx}" ixy="${foot_ixy}" ixz="${foot_ixz}"
                        iyy="${foot_iyy}" iyz="${foot_iyz}"
                        izz="${foot_izz}"/>
            </inertial> -->
  </link>
  <gazebo reference="RF_foot_fixed">
    <disableFixedJointLumping>true</disableFixedJointLumping>
  </gazebo>
  <transmission name="RF_HAA_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="RF_HAA">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="RF_HAA_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="RF_HFE_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="RF_HFE">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="RF_HFE_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="RF_KFE_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="RF_KFE">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="RF_KFE_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <joint name="LF_HAA" type="revolute">
    <origin rpy="0 0 0" xyz="0.21 0.055 0.0"/>
    <parent link="base"/>
    <child link="LF_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-1.0471975511965976" upper="1.0471975511965976" velocity="20.0"/>
  </joint>
  <link name="LF_hip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://legged_robs3go_description/meshes/robs3go/FL_hip_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.028" radius="0.056"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00234792 0.00037524 -9.86e-05"/>
      <mass value="0.95120267"/>
      <inertia ixx="0.0007136" ixy="8.15e-06" ixz="-3.85e-06" iyy="0.00116168" iyz="-3.3e-07" izz="0.00079101"/>
    </inertial>
  </link>
  <joint name="LF_HFE" type="revolute">
    <!-- c -->
    <!-- <origin rpy="0 0 0" xyz="0 ${thigh_offset*mirror} 0"/> -->
    <origin rpy="0 0.0 0" xyz="0 0.09772 0"/>
    <parent link="LF_hip"/>
    <child link="LF_thigh"/>
    <!-- <axis xyz="0 -1 0"/> -->
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-1" upper="3.18871654339364" velocity="20.0"/>
  </joint>
  <link name="LF_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://legged_robs3go_description/meshes/robs3go/FL_thigh_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.12"/>
      <geometry>
        <box size="0.24 0.023 0.046"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.053" radius="0.056"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00370878 -0.02484263 -0.02475346"/>
      <mass value="1.35348511"/>
      <inertia ixx="0.00599998" ixy="0.00013853" ixz="-0.00051908" iyy="0.00615951" iyz="0.00083398" izz="0.00132322"/>
    </inertial>
  </link>
  <joint name="LF_KFE" type="revolute">
    <!-- cc -->
    <origin rpy="0 0 0" xyz="0 0 -0.24"/>
    <parent link="LF_thigh"/>
    <child link="LF_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-2.743657584135086" upper="-0.6510078109938848" velocity="20.0"/>
  </joint>
  <link name="LF_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://legged_robs3go_description/meshes/robs3go/FL_calf_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.12"/>
      <geometry>
        <box size="0.24 0.023 0.045"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.00901673 -6.91e-06 -0.14084939"/>
      <mass value="0.21273957"/>
      <inertia ixx="0.00177407" ixy="-2e-08" ixz="8.082e-05" iyy="0.0017994" iyz="2.4e-07" izz="5.853e-05"/>
    </inertial>
  </link>
  <joint name="LF_foot_fixed" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 -0.24"/>
    <parent link="LF_calf"/>
    <child link="LF_FOOT"/>
  </joint>
  <link name="LF_FOOT">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
        <!-- <mesh filename="package://legged_robs3go_description/meshes/$(arg robot_type)/FL_toe_link.STL" scale="1 1 1"/> -->
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.0450952"/>
      <inertia ixx="1.1273800000000004e-05" ixy="0.0" ixz="0.0" iyy="1.1273800000000004e-05" iyz="0.0" izz="1.1273800000000004e-05"/>
    </inertial>
    <!-- <inertial>
                <mass value="${0.0}"/>
                <inertia
                        ixx="0.0" ixy="0.0" ixz="0.0"
                        iyy="0.0" iyz="0.0"
                        izz="0.0"/>
            </inertial> -->
    <!-- <inertial>
                <mass value="${foot_mass}"/>
                <origin rpy="0 0 0" xyz="${foot_com_x} ${foot_com_y} ${foot_com_z}"/>
                <inertia
                        ixx="${foot_ixx}" ixy="${foot_ixy}" ixz="${foot_ixz}"
                        iyy="${foot_iyy}" iyz="${foot_iyz}"
                        izz="${foot_izz}"/>
            </inertial> -->
  </link>
  <gazebo reference="LF_foot_fixed">
    <disableFixedJointLumping>true</disableFixedJointLumping>
  </gazebo>
  <transmission name="LF_HAA_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="LF_HAA">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="LF_HAA_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="LF_HFE_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="LF_HFE">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="LF_HFE_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="LF_KFE_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="LF_KFE">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="LF_KFE_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <joint name="RH_HAA" type="revolute">
    <origin rpy="0 0 0" xyz="-0.21 -0.055 0.0"/>
    <parent link="base"/>
    <child link="RH_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-1.0471975511965976" upper="1.0471975511965976" velocity="20.0"/>
  </joint>
  <link name="RH_hip">
    <visual>
      <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://legged_robs3go_description/meshes/robs3go/FL_hip_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.028" radius="0.056"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.00234792 -0.00037524 -9.86e-05"/>
      <mass value="0.95120267"/>
      <inertia ixx="0.0007136" ixy="8.15e-06" ixz="3.85e-06" iyy="0.00116168" iyz="3.3e-07" izz="0.00079101"/>
    </inertial>
  </link>
  <joint name="RH_HFE" type="revolute">
    <!-- c -->
    <!-- <origin rpy="0 0 0" xyz="0 ${thigh_offset*mirror} 0"/> -->
    <origin rpy="0 0.0 0" xyz="0 -0.09772 0"/>
    <parent link="RH_hip"/>
    <child link="RH_thigh"/>
    <!-- <axis xyz="0 -1 0"/> -->
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-1" upper="3.18871654339364" velocity="20.0"/>
  </joint>
  <link name="RH_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://legged_robs3go_description/meshes/robs3go/FR_thigh_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.12"/>
      <geometry>
        <box size="0.24 0.023 0.046"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.053" radius="0.056"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00370878 0.02484263 -0.02475346"/>
      <mass value="1.35348511"/>
      <inertia ixx="0.00599998" ixy="-0.00013853" ixz="-0.00051908" iyy="0.00615951" iyz="-0.00083398" izz="0.00132322"/>
    </inertial>
  </link>
  <joint name="RH_KFE" type="revolute">
    <!-- cc -->
    <origin rpy="0 0 0" xyz="0 0 -0.24"/>
    <parent link="RH_thigh"/>
    <child link="RH_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-2.743657584135086" upper="-0.6510078109938848" velocity="20.0"/>
  </joint>
  <link name="RH_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://legged_robs3go_description/meshes/robs3go/FR_calf_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.12"/>
      <geometry>
        <box size="0.24 0.023 0.045"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.00901673 -6.91e-06 -0.14084939"/>
      <mass value="0.21273957"/>
      <inertia ixx="0.00177407" ixy="-2e-08" ixz="8.082e-05" iyy="0.0017994" iyz="2.4e-07" izz="5.853e-05"/>
    </inertial>
  </link>
  <joint name="RH_foot_fixed" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 -0.24"/>
    <parent link="RH_calf"/>
    <child link="RH_FOOT"/>
  </joint>
  <link name="RH_FOOT">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
        <!-- <mesh filename="package://legged_robs3go_description/meshes/$(arg robot_type)/FL_toe_link.STL" scale="1 1 1"/> -->
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.0450952"/>
      <inertia ixx="1.1273800000000004e-05" ixy="0.0" ixz="0.0" iyy="1.1273800000000004e-05" iyz="0.0" izz="1.1273800000000004e-05"/>
    </inertial>
    <!-- <inertial>
                <mass value="${0.0}"/>
                <inertia
                        ixx="0.0" ixy="0.0" ixz="0.0"
                        iyy="0.0" iyz="0.0"
                        izz="0.0"/>
            </inertial> -->
    <!-- <inertial>
                <mass value="${foot_mass}"/>
                <origin rpy="0 0 0" xyz="${foot_com_x} ${foot_com_y} ${foot_com_z}"/>
                <inertia
                        ixx="${foot_ixx}" ixy="${foot_ixy}" ixz="${foot_ixz}"
                        iyy="${foot_iyy}" iyz="${foot_iyz}"
                        izz="${foot_izz}"/>
            </inertial> -->
  </link>
  <gazebo reference="RH_foot_fixed">
    <disableFixedJointLumping>true</disableFixedJointLumping>
  </gazebo>
  <transmission name="RH_HAA_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="RH_HAA">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="RH_HAA_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="RH_HFE_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="RH_HFE">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="RH_HFE_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="RH_KFE_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="RH_KFE">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="RH_KFE_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <joint name="LH_HAA" type="revolute">
    <origin rpy="0 0 0" xyz="-0.21 0.055 0.0"/>
    <parent link="base"/>
    <child link="LH_hip"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-1.0471975511965976" upper="1.0471975511965976" velocity="20.0"/>
  </joint>
  <link name="LH_hip">
    <visual>
      <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://legged_robs3go_description/meshes/robs3go/FR_hip_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.028" radius="0.056"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.00234792 0.00037524 -9.86e-05"/>
      <mass value="0.95120267"/>
      <inertia ixx="0.0007136" ixy="-8.15e-06" ixz="3.85e-06" iyy="0.00116168" iyz="-3.3e-07" izz="0.00079101"/>
    </inertial>
  </link>
  <joint name="LH_HFE" type="revolute">
    <!-- c -->
    <!-- <origin rpy="0 0 0" xyz="0 ${thigh_offset*mirror} 0"/> -->
    <origin rpy="0 0.0 0" xyz="0 0.09772 0"/>
    <parent link="LH_hip"/>
    <child link="LH_thigh"/>
    <!-- <axis xyz="0 -1 0"/> -->
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-1" upper="3.18871654339364" velocity="20.0"/>
  </joint>
  <link name="LH_thigh">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://legged_robs3go_description/meshes/robs3go/FL_thigh_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.12"/>
      <geometry>
        <box size="0.24 0.023 0.046"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.053" radius="0.056"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00370878 -0.02484263 -0.02475346"/>
      <mass value="1.35348511"/>
      <inertia ixx="0.00599998" ixy="0.00013853" ixz="-0.00051908" iyy="0.00615951" iyz="0.00083398" izz="0.00132322"/>
    </inertial>
  </link>
  <joint name="LH_KFE" type="revolute">
    <!-- cc -->
    <origin rpy="0 0 0" xyz="0 0 -0.24"/>
    <parent link="LH_thigh"/>
    <child link="LH_calf"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="0.0" friction="0.0"/>
    <limit effort="60.0" lower="-2.743657584135086" upper="-0.6510078109938848" velocity="20.0"/>
  </joint>
  <link name="LH_calf">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://legged_robs3go_description/meshes/robs3go/FL_calf_link.STL" scale="1 1 1"/>
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 1.5707963267948966 0" xyz="0 0 -0.12"/>
      <geometry>
        <box size="0.24 0.023 0.045"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.00901673 -6.91e-06 -0.14084939"/>
      <mass value="0.21273957"/>
      <inertia ixx="0.00177407" ixy="-2e-08" ixz="8.082e-05" iyy="0.0017994" iyz="2.4e-07" izz="5.853e-05"/>
    </inertial>
  </link>
  <joint name="LH_foot_fixed" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 -0.24"/>
    <parent link="LH_calf"/>
    <child link="LH_FOOT"/>
  </joint>
  <link name="LH_FOOT">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
        <!-- <mesh filename="package://legged_robs3go_description/meshes/$(arg robot_type)/FL_toe_link.STL" scale="1 1 1"/> -->
      </geometry>
      <material name="white"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.025"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.0450952"/>
      <inertia ixx="1.1273800000000004e-05" ixy="0.0" ixz="0.0" iyy="1.1273800000000004e-05" iyz="0.0" izz="1.1273800000000004e-05"/>
    </inertial>
    <!-- <inertial>
                <mass value="${0.0}"/>
                <inertia
                        ixx="0.0" ixy="0.0" ixz="0.0"
                        iyy="0.0" iyz="0.0"
                        izz="0.0"/>
            </inertial> -->
    <!-- <inertial>
                <mass value="${foot_mass}"/>
                <origin rpy="0 0 0" xyz="${foot_com_x} ${foot_com_y} ${foot_com_z}"/>
                <inertia
                        ixx="${foot_ixx}" ixy="${foot_ixy}" ixz="${foot_ixz}"
                        iyy="${foot_iyy}" iyz="${foot_iyz}"
                        izz="${foot_izz}"/>
            </inertial> -->
  </link>
  <gazebo reference="LH_foot_fixed">
    <disableFixedJointLumping>true</disableFixedJointLumping>
  </gazebo>
  <transmission name="LH_HAA_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="LH_HAA">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="LH_HAA_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="LH_HFE_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="LH_HFE">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="LH_HFE_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="LH_KFE_tran">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="LH_KFE">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="LH_KFE_motor">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <!-- Robot Footprint -->
  <!-- <joint name="footprint_joint" type="fixed">
        <parent link="base"/>
        <child link="base_footprint"/>
        <origin xyz="0.0 0.0 -0.2" rpy="0 0 0"/>
    </joint>

    <link name="base_footprint">
      <inertial>
      <origin xyz="0 0 0" rpy="${pi/2} 0 ${pi/2}"/>      
      <mass value="0.001"/>
      <inertia ixx="0.0" ixy="0.0" ixz="0.0" iyy="0.0" iyz="0.0" izz="0.0"/>
    </inertial>
    </link>

    <xacro:lidar parent="base" xyz="-0.065 0. 0.1" rpy="0. 0. 0."/> -->
</robot>

