# RSL RL
Fast and simple implementation of RL algorithms, designed to run fully on GPU.
This code is an evolution of `rl-pytorch` provided with NVIDIA's <PERSON>.

Only PPO is implemented for now. More algorithms will be added later.
Contributions are welcome.

## Setup

```
git clone https://github.com/leggedrobotics/rsl_rl
cd rsl_rl
pip install -e .
```

### Useful Links ###
Example use case: https://github.com/leggedrobotics/legged_gym  
Project website: https://leggedrobotics.github.io/legged_gym/  
Paper: https://arxiv.org/abs/2109.11978

**Maintainer**: <PERSON><PERSON>  
**Affiliation**: Robotic Systems Lab, ETH Zurich & NVIDIA  
**Contact**: <EMAIL>  



步态奇丑 gazebo仿真中会不断往前倒 issac gym中不会这样 
issue说降低base_height-->稳定性变强